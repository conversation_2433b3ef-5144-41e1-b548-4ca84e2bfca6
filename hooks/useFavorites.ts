import { useState, useEffect } from 'react';
import { SavedRecipesService } from '@/services/SavedRecipesService';
import { Recipe, InstructionType } from '@/components/types';
import { useAuth } from '@/contexts/AuthContext';
import { generateRecipeDetailsAsync } from '@/services/generateRecipes';

export interface UseFavoritesReturn {
  favorites: Set<string>;
  toggleFavorite: (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => void;
  loadingFavorites: boolean;
  generatingDetails: Set<string>;
}

export interface UseFavoritesProps {
  onRecipeSaved?: () => void;
  onRecipeDetailsGenerated?: (recipeId: string, ingredients: any[], instructions: any) => void;
}

export const useFavorites = (props?: UseFavoritesProps): UseFavoritesReturn => {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [loadingFavorites, setLoadingFavorites] = useState(true);
  const [generatingDetails, setGeneratingDetails] = useState<Set<string>>(new Set());
  const { user } = useAuth();
  const { onRecipeSaved, onRecipeDetailsGenerated } = props || {};

  // Helper function to check if a recipe has complete instructions
  const hasCompleteInstructions = (recipe: Recipe): boolean => {
    return (
      recipe.ingredients.length > 0 &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== '' &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== 'Instructions not available'
    );
  };

  // Load saved recipe IDs when user is available
  useEffect(() => {
    const loadSavedRecipes = async () => {
      if (!user) {
        setFavorites(new Set());
        setLoadingFavorites(false);
        return;
      }

      try {
        setLoadingFavorites(true);
        const savedRecipeIds = await SavedRecipesService.getSavedRecipeIds();
        setFavorites(new Set(savedRecipeIds));
      } catch (error) {
        console.error('Error loading saved recipes:', error);
        setFavorites(new Set());
      } finally {
        setLoadingFavorites(false);
      }
    };

    loadSavedRecipes();
  }, [user]);

  const toggleFavorite = async (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => {
    e.stopPropagation();

    if (!user) {
      console.log('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      const newFavorites = new Set(favorites);

      if (favorites.has(recipeId)) {
        // Remove from favorites
        await SavedRecipesService.unsaveRecipe(recipeId);
        newFavorites.delete(recipeId);
        setFavorites(newFavorites);
      } else {
        // Add to favorites
        if (!recipe) {
          console.error('Recipe object is required to save a recipe');
          return;
        }

        // Update favorites state immediately for UI feedback
        newFavorites.add(recipeId);
        setFavorites(newFavorites);

        // Save the recipe (potentially incomplete) first for immediate favoriting
        await SavedRecipesService.saveRecipe(recipe);

        // Call the callback to refresh saved recipes if provided
        if (onRecipeSaved) {
          onRecipeSaved();
        }

        // Check if recipe has complete instructions and generate them asynchronously
        if (!hasCompleteInstructions(recipe)) {
          console.log('Recipe missing instructions, generating details in background...');

          // Only generate details if we have a lastResponseId (for LLM-generated recipes)
          if (lastResponseId) {
            // Mark recipe as generating details
            setGeneratingDetails((prev) => new Set(prev).add(recipe.id));

            // Generate details in the background without blocking UI
            generateRecipeDetailsAsync(recipe.id, recipe.title, recipe.mealType, lastResponseId)
              .then((details) => {
                // Create a complete recipe with the generated details
                const recipeToSave = {
                  ...recipe,
                  ingredients: details.ingredients,
                  instructions: details.instructions,
                };

                console.log('Recipe details generated successfully, updating saved recipe...');

                // Update the saved recipe with complete details
                SavedRecipesService.saveRecipe(recipeToSave)
                  .then(() => {
                    // Call the callback to update the local recipe state
                    if (onRecipeDetailsGenerated) {
                      onRecipeDetailsGenerated(recipe.id, details.ingredients, details.instructions);
                    }

                    // Refresh saved recipes to show updated details
                    if (onRecipeSaved) {
                      onRecipeSaved();
                    }

                    // Remove from generating details set
                    setGeneratingDetails((prev) => {
                      const newSet = new Set(prev);
                      newSet.delete(recipe.id);
                      return newSet;
                    });
                  })
                  .catch((error) => {
                    console.error('Error updating saved recipe with details:', error);
                    // Remove from generating details set even on error
                    setGeneratingDetails((prev) => {
                      const newSet = new Set(prev);
                      newSet.delete(recipe.id);
                      return newSet;
                    });
                  });
              })
              .catch((error) => {
                console.error('Error generating recipe details in background:', error);
                // Remove from generating details set on error
                setGeneratingDetails((prev) => {
                  const newSet = new Set(prev);
                  newSet.delete(recipe.id);
                  return newSet;
                });
              });
          } else {
            console.log('No lastResponseId available, recipe saved without generating details');
          }
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return {
    favorites,
    toggleFavorite,
    loadingFavorites,
    generatingDetails,
  };
};
