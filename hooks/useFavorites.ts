import { useState, useEffect } from 'react';
import { SavedRecipesService } from '@/services/SavedRecipesService';
import { Recipe, InstructionType } from '@/components/types';
import { useAuth } from '@/contexts/AuthContext';
import { generateRecipeDetailsAsync } from '@/services/generateRecipes';

export interface UseFavoritesReturn {
  favorites: Set<string>;
  toggleFavorite: (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => void;
  loadingFavorites: boolean;
}

export interface UseFavoritesProps {
  onRecipeSaved?: () => void;
}

export const useFavorites = (props?: UseFavoritesProps): UseFavoritesReturn => {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [loadingFavorites, setLoadingFavorites] = useState(true);
  const { user } = useAuth();
  const { onRecipeSaved } = props || {};

  // Helper function to check if a recipe has complete instructions
  const hasCompleteInstructions = (recipe: Recipe): boolean => {
    return (
      recipe.ingredients.length > 0 &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== '' &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== 'Instructions not available'
    );
  };

  // Load saved recipe IDs when user is available
  useEffect(() => {
    const loadSavedRecipes = async () => {
      if (!user) {
        setFavorites(new Set());
        setLoadingFavorites(false);
        return;
      }

      try {
        setLoadingFavorites(true);
        const savedRecipeIds = await SavedRecipesService.getSavedRecipeIds();
        setFavorites(new Set(savedRecipeIds));
      } catch (error) {
        console.error('Error loading saved recipes:', error);
        setFavorites(new Set());
      } finally {
        setLoadingFavorites(false);
      }
    };

    loadSavedRecipes();
  }, [user]);

  const toggleFavorite = async (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => {
    e.stopPropagation();

    if (!user) {
      console.log('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      const newFavorites = new Set(favorites);

      if (favorites.has(recipeId)) {
        // Remove from favorites
        await SavedRecipesService.unsaveRecipe(recipeId);
        newFavorites.delete(recipeId);
      } else {
        // Add to favorites
        if (!recipe) {
          console.error('Recipe object is required to save a recipe');
          return;
        }

        let recipeToSave = recipe;
        newFavorites.add(recipeId);

        // Check if recipe has complete instructions
        if (!hasCompleteInstructions(recipe)) {
          console.log('Recipe missing instructions, generating details before saving...');

          // Only generate details if we have a lastResponseId (for LLM-generated recipes)
          if (lastResponseId) {
            try {
              const details = await generateRecipeDetailsAsync(
                recipe.id,
                recipe.title,
                recipe.mealType,
                lastResponseId
              );

              // Create a complete recipe with the generated details
              recipeToSave = {
                ...recipe,
                ingredients: details.ingredients,
                instructions: details.instructions,
              };

              console.log('Recipe details generated successfully for saving');
            } catch (error) {
              console.error('Error generating recipe details for saving:', error);
              // Continue with saving the incomplete recipe rather than failing completely
            }
          } else {
            console.log('No lastResponseId available, saving recipe without generating details');
          }
        }

        await SavedRecipesService.saveRecipe(recipeToSave);

        // Call the callback to refresh saved recipes if provided
        if (onRecipeSaved) {
          onRecipeSaved();
        }
      }

      setFavorites(newFavorites);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return {
    favorites,
    toggleFavorite,
    loadingFavorites,
  };
};
