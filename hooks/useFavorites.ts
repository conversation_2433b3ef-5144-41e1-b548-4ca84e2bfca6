import { useState, useEffect } from 'react';
import { SavedRecipesService } from '@/services/SavedRecipesService';
import { Recipe } from '@/components/types';
import { useAuth } from '@/contexts/AuthContext';

export interface UseFavoritesReturn {
  favorites: Set<string>;
  toggleFavorite: (recipeId: string, e: any, recipe?: Recipe) => void;
  loadingFavorites: boolean;
}

export const useFavorites = (): UseFavoritesReturn => {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [loadingFavorites, setLoadingFavorites] = useState(true);
  const { user } = useAuth();

  // Load saved recipe IDs when user is available
  useEffect(() => {
    const loadSavedRecipes = async () => {
      if (!user) {
        setFavorites(new Set());
        setLoadingFavorites(false);
        return;
      }

      try {
        setLoadingFavorites(true);
        const savedRecipeIds = await SavedRecipesService.getSavedRecipeIds();
        setFavorites(new Set(savedRecipeIds));
      } catch (error) {
        console.error('Error loading saved recipes:', error);
        setFavorites(new Set());
      } finally {
        setLoadingFavorites(false);
      }
    };

    loadSavedRecipes();
  }, [user]);

  const toggleFavorite = async (recipeId: string, e: any, recipe?: Recipe) => {
    e.stopPropagation();

    if (!user) {
      console.log('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      const newFavorites = new Set(favorites);

      if (favorites.has(recipeId)) {
        // Remove from favorites
        await SavedRecipesService.unsaveRecipe(recipeId);
        newFavorites.delete(recipeId);
      } else {
        // Add to favorites
        if (!recipe) {
          console.error('Recipe object is required to save a recipe');
          return;
        }
        await SavedRecipesService.saveRecipe(recipe);
        newFavorites.add(recipeId);
      }

      setFavorites(newFavorites);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return {
    favorites,
    toggleFavorite,
    loadingFavorites,
  };
};
