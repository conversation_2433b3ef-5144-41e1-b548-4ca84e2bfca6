import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import createRecipeStyles from '@/styles/RecipeStyles';
import LoadingAnimation from '@/components/LoadingAnimation';
import RecipeCard from '@/components/RecipeCard';
import ExpandedRecipeCard from '@/components/ExpandedRecipeCard';
import { 
  Recipe, 
  RecipeVariant, 
  InstructionType 
} from '@/components/types';

interface RecipeListProps {
  recipes: Recipe[];
  isLoading: boolean;
  refreshing: boolean;
  loadingMore: boolean;
  error: string | null;
  canLoadMore: boolean;
  expandedRecipeIds: Set<string>;
  favorites: Set<string>;
  servings: number;
  selectedVariant: RecipeVariant;
  instructionType: InstructionType;
  recipeVariants: RecipeVariant[];
  instructionTypes: InstructionType[];
  loadingRecipeDetails: string | null;
  lastResponseId: string | null;
  isPollingForFirstTimeRecipes?: boolean;
  onToggleFavorite: (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => void;
  onToggleExpanded: (recipeId: string) => void;
  onSelectVariant: (variant: RecipeVariant) => void;
  onSelectInstructionType: (type: InstructionType) => void;
  onChangeServings: (servings: number) => void;
}

const RecipeList: React.FC<RecipeListProps> = ({
  recipes,
  isLoading,
  refreshing,
  loadingMore,
  error,
  canLoadMore,
  expandedRecipeIds,
  favorites,
  servings,
  selectedVariant,
  instructionType,
  recipeVariants,
  instructionTypes,
  loadingRecipeDetails,
  lastResponseId,
  isPollingForFirstTimeRecipes = false,
  onToggleFavorite,
  onToggleExpanded,
  onSelectVariant,
  onSelectInstructionType,
  onChangeServings,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const recipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');

  const renderRecipeCard = (recipe: Recipe) => {
    const isExpanded = expandedRecipeIds.has(recipe.id);
    const isFavorite = favorites.has(recipe.id);
    const isLoadingDetails = loadingRecipeDetails === recipe.id;

    if (isExpanded) {
      return (
        <View key={recipe.id} style={recipeStyles.recipeCardContainer}>
          <ExpandedRecipeCard
            title={recipe.title}
            timeInMinutes={recipe.timeInMinutes}
            calories={recipe.calories}
            imageUrl={recipe.imageUrl}
            instructions={recipe.instructions}
            ingredients={recipe.ingredients}
            isFavorite={isFavorite}
            servings={servings}
            selectedVariant={selectedVariant}
            instructionType={instructionType}
            recipeVariants={recipeVariants}
            instructionTypes={instructionTypes}
            isLoading={isLoadingDetails}
            onToggleFavorite={(e: any) => onToggleFavorite(recipe.id, e, recipe, lastResponseId)}
            onSelectVariant={onSelectVariant}
            onSelectInstructionType={onSelectInstructionType}
            onChangeServings={onChangeServings}
            onPress={() => onToggleExpanded(recipe.id)}
          />
        </View>
      );
    }

    return (
      <View key={recipe.id} style={recipeStyles.recipeCardContainer}>
        <RecipeCard
          title={recipe.title}
          timeInMinutes={recipe.timeInMinutes}
          calories={recipe.calories}
          imageUrl={recipe.imageUrl}
          isFavorite={isFavorite}
          onToggleFavorite={(e: any) => onToggleFavorite(recipe.id, e, recipe, lastResponseId)}
          onPress={() => onToggleExpanded(recipe.id)}
        />
      </View>
    );
  };

  // Error state
  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ color: 'red', textAlign: 'center' }}>{error}</Text>
      </View>
    );
  }

  // Loading state
  if (isLoading || refreshing) {
    return (
      <View style={recipeStyles.loadingContainer}>
        <LoadingAnimation
          source={require('../assets/images/gifs/fry-pan.gif')}
          message='Loading delicious recipes for you...'
        />
      </View>
    );
  }

  // Special loading state for first-time recipe polling
  if (isPollingForFirstTimeRecipes && recipes.length === 0) {
    return (
      <View style={recipeStyles.loadingContainer}>
        <LoadingAnimation
          source={require('../assets/images/gifs/fry-pan.gif')}
          message='Crafting your personalized recipes for the first time. They will be ready within 3 minutes 👩‍🍳'
        />
      </View>
    );
  }

  return (
    <>
      {/* Recipe Cards */}
      {recipes.map(renderRecipeCard)}

      {/* Load More Indicator */}
      {canLoadMore && !isLoading && !refreshing && !error && (
        <View style={{ padding: 20, alignItems: 'center' }}>
          {loadingMore && !refreshing ? (
            <ActivityIndicator size='small' color={colors.accent} />
          ) : (
            <Text style={{ color: colors.text }}>Scroll for more recipes</Text>
          )}
        </View>
      )}
    </>
  );
};

export default RecipeList;
